import 'dart:typed_data';

import 'package:connectone/bai_models/get_projects_res.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/site_access_type_res.dart';
import 'package:connectone/bai_models/units_res.dart';

/// This class stores the items added to the cart by the user.
/// The items are a list of [StockItem] and the project selected by the user.
/// The delivery date, site access, road access, site access type list
/// and units are also stored.
/// The selected unit and order group id are used to store the selected
/// unit and the order group id of the items in the cart.
class BaiCart {
  /// The list of items in the cart.
  static List<StockItem> cartItems = [];

  /// The project selected by the user.
  static Project? project;

  /// The delivery date selected by the user.
  static DateTime? deliveryDate;

  /// The site access selected by the user.
  static String? siteAccess;

  /// The road access selected by the user.
  static String? roadAccess;

  /// The site access type list.
  static SiteAccessTypeList? siteAccessTypeList;

  /// The units.
  static UnitsRes? units;

  /// The selected unit.
  static int? selectedUnit;

  /// The order group id.
  static int? orderGroupId;

  /// Checks if a new item with the given category ID is in the same category
  /// as any existing item in the cart.
  /// It compares the provided `categoryID` with the `cappcategoriesid` of
  /// the `poStockItem` of existing items in the cart.
  /// Returns `true` if a matching category is found, `false` otherwise.
  static bool isCategoryAllowed(int categoryID, {int? splitCategoryId}) {
    // if any items have cappcategoriesid == -3, return true
    if (cartItems
        .any((element) => element.poStockItem?.cappCategoriesId == -3)) {
      return true;
    }

    if (splitCategoryId == -3) {
      return true;
    }

    if (splitCategoryId != null) {
      if (splitCategoryId == categoryID) {
        return true;
      } else {
        return false;
      }
    }

    if (cartItems.isEmpty) {
      return true;
    }

    for (final existingItem in cartItems) {
      if (existingItem.poStockItem?.cappCategoriesId == null) {
        continue;
      }
      if (categoryID == existingItem.poStockItem?.cappCategoriesId) {
        return true;
      }
    }
    return false;
  }

  static bool hasSplitItems() {
    // if (cartItems.length == 1) {
    //   return false;
    // }
    for (final existingItem in cartItems) {
      if (existingItem.poStockItem?.prchOrdrSplitId != null) {
        return true;
      }
    }
    return false;
  }

  // Returns the group ID of the last item in the cart, if available.
  static int? latestSplitGroupId() {
    if (cartItems.isNotEmpty) {
      return cartItems.last.poStockItem?.prchOrdrSplitId;
    }
    return null;
  }
}

class StockItem {
  PoStockItem? poStockItem;
  List<String>? audios;
  List<String>? images;
  List<Uint8List>? imageBytes;
  List<String>? files;
  List<Uint8List>? fileBytes;
  String? selectedUnit;
  StockItem({
    required this.poStockItem,
    required this.audios,
    required this.images,
    required this.imageBytes,
    required this.files,
    required this.fileBytes,
    required this.selectedUnit,
  });
}
