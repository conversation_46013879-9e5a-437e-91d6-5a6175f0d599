import 'package:audioplayers/audioplayers.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/accept_negotiation_req.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/core/bai_widgets/negotiate_dialog.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:url_launcher/url_launcher.dart';

class QuotesDialog extends StatefulWidget {
  final NegotiationHistory? negotiationHistory;
  final Offer? offer;
  final bool isLatestItem;

  const QuotesDialog({
    Key? key,
    this.negotiationHistory,
    this.offer,
    this.isLatestItem = false,
  }) : super(key: key);

  @override
  State<QuotesDialog> createState() => _QuotesDialogState();
}

class _QuotesDialogState extends State<QuotesDialog> {
  final FlutterSoundPlayer _mPlayer = FlutterSoundPlayer();
  // var theSource = AudioSource.microphone;
  final bool _mPlayerIsInited = false;
  final bool _mplaybackReady = false;
  bool playing = false;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];
  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _mPlayer.openPlayer();
    _setArrays(widget.negotiationHistory);
  }

  void _setArrays(NegotiationHistory? negotiationHistory) {
    // final List<Media> items = [
    //   Media(url: "https://fileinfo.com/img/ss/md/jpg_44-3.jpg"),
    //   Media(url: "https://fileinfo.com/img/ss/md/jpg_44-3.jpg"),
    //   Media(url: "https://morth.nic.in/sites/default/files/dd12-13_0.pdf"),
    //   Media(url: "https://www2.cs.uic.edu/~i101/SoundFiles/CantinaBand60.wav")
    // ];
    final List<Media> items = widget.negotiationHistory?.medias ?? [];
    for (var media in items) {
      final String? url = media.url;
      if (url == null) {
        continue;
      }
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady) {
      return null;
    }
    return _mPlayer.isStopped ? play() : stopPlayer();
  }

  void play() {
    assert(_mPlayerIsInited && _mplaybackReady && _mPlayer.isStopped);
    _mPlayer.startPlayer(whenFinished: () {
      setState(() {});
    }).then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  @override

  /// Builds a dialog that displays quotes for a negotiation.
  ///
  /// The dialog displays the vendor's offer, the buyer's counter offer, and
  /// any additional comments or attachments.
  ///
  /// If the negotiation is open, the dialog also displays buttons for the
  /// buyer to accept or negotiate the offer.
  ///
  /// When the dialog is closed, the parent widget is notified with the
  /// [NegotiationHistory] object.
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 52,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'Quotes - Negotiation',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ).withCloseButton(() => Navigator.pop(context)),
              const SizedBox(height: 20),
              RatioText(
                text1: "Quantity:",
                text2: widget.offer?.quantity.toString() ?? "N/A",
              ),
              const SizedBox(height: 6),
              RatioText(
                text1: "Price / Qty:",
                text2: "₹ ${_getPricePerQuantity()}",
              ),
              const SizedBox(height: 10),
              Container(
                height: 40,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryColorOld,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Offer:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '₹ ${formatToIndianRupee(widget.negotiationHistory?.offerPrice?.toString() ?? "0")}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              RatioText(
                text1: "Remarks:",
                text2: widget.negotiationHistory?.remarks ?? "N/A",
              ),
              const SizedBox(height: 6),
              if (images.isNotEmpty) const SizedBox(height: 16),
              if (images.isNotEmpty)
                SizedBox(
                  height: 74,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: images.length,
                    shrinkWrap: true,
                    padding: const EdgeInsets.all(2),
                    itemBuilder: (context, index) {
                      return Material(
                        elevation: 2,
                        borderRadius: BorderRadius.circular(8.0),
                        clipBehavior: Clip.hardEdge,
                        child: GestureDetector(
                            onTap: () async {
                              showImageDialog(context, images[index]);
                            },
                            child: Image.network(
                              images[index],
                              fit: BoxFit.fill,
                            )),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(width: 8);
                    },
                  ),
                ),
              if (files.isNotEmpty) const SizedBox(height: 16),
              if (files.isNotEmpty)
                SizedBox(
                  height: 74,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: files.length,
                    shrinkWrap: true,
                    padding: const EdgeInsets.all(2),
                    itemBuilder: (context, index) {
                      var url = Uri.parse(files[index]);
                      return GestureDetector(
                        onTap: () async {
                          if (await canLaunchUrl(url)) {
                            await launchUrl(url);
                          } else {
                            throw 'Could not launch $url';
                          }
                        },
                        child: Material(
                          elevation: 2,
                          borderRadius: BorderRadius.circular(8.0),
                          clipBehavior: Clip.hardEdge,
                          child: Container(
                            color: Colors.grey.shade200,
                            height: 72,
                            width: 72,
                            margin: const EdgeInsets.all(0.25),
                            child: Center(
                              child: Icon(
                                Icons.file_copy_outlined,
                                size: 32.0,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(width: 8);
                    },
                  ),
                ),
              if (audios.isNotEmpty)
                const Padding(
                  padding: EdgeInsets.only(left: 0, top: 12),
                  child: Row(
                    children: [
                      Text(
                        "Voice Notes",
                        style: TextStyle(
                            color: Colors.black, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              if (audios.isNotEmpty) const SizedBox(height: 8),
              if (audios.isNotEmpty)
                Row(
                  children: [
                    GestureDetector(
                      onTap: _togglePlayback,
                      child: Icon(
                        playing ? Icons.pause : Icons.play_circle_fill,
                        size: 40.0,
                        color: AppColors.primaryColorOld,
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Container(
                      width: 200.0,
                      height: 40.0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Image.asset("assets/images/waveform.png"),
                    ),
                  ],
                ),
              if (widget.offer?.statusCd != "APPR" &&
                  widget.offer?.statusCd != "RJCT" &&
                  widget.offer?.negotiationStatus != "ACPT" &&
                  widget.isLatestItem)
                const SizedBox(height: 20),
              if (widget.offer?.statusCd != "APPR" &&
                  widget.offer?.statusCd != "RJCT" &&
                  widget.offer?.negotiationStatus != "ACPT" &&
                  widget.isLatestItem)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return NegotiateDialog(
                                    offer: widget.offer!,
                                    unit: "Unit",
                                    history: widget.negotiationHistory,
                                  );
                                });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.darkRed,
                          ),
                          child: const Text(
                            'NEGOTIATE',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    if (!isBuyer()) const SizedBox(width: 16),
                    if (!isBuyer())
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: ElevatedButton(
                            onPressed: () async {
                              try {
                                var req = AcceptNegotiationReq(
                                  negotiationStatus: "ACPT",
                                );
                                context.read<OffersCubit>().acceptNegotiation(
                                      widget.offer?.id.toString() ?? "0",
                                      req,
                                    );
                                Navigator.of(context).pop();
                              } catch (e) {
                                alert(
                                    'Failed to accept negotiation: ${e.toString()}');
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.green,
                            ),
                            child: const Text(
                              'ACCEPT',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }

  num _getPricePerQuantity() {
    return ((widget.negotiationHistory?.offerPrice ?? 0) /
            (widget.offer?.quantity ?? 0))
        .round();
  }
}

class RatioText extends StatelessWidget {
  final String text1;
  final String text2;
  final TextStyle? textStyle1;
  final TextStyle? textStyle2;
  final bool? highlight;

  const RatioText({
    Key? key,
    required this.text1,
    required this.text2,
    this.textStyle1,
    this.textStyle2,
    this.highlight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2, // This will take 2 parts of the total 5
          child: Container(
            alignment: Alignment.centerLeft,
            child: Text(
              text1,
              style: textStyle1 ??
                  TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    backgroundColor:
                        highlight == true ? Colors.green.shade300 : null,
                  ),
            ),
          ),
        ),
        Expanded(
          flex: 3, // This will take 3 parts of the total 5
          child: Container(
            alignment: Alignment.centerLeft,
            child: Text(
              text2,
              style: textStyle2 ??
                  TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    backgroundColor:
                        highlight == true ? Colors.green.shade300 : null,
                  ),
            ),
          ),
        ),
      ],
    );
  }
}
