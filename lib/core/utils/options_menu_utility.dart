import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/bai_screens/edit_product.dart';
import 'package:connectone/bai_screens/mr_grouping.dart';
import 'package:connectone/bai_screens/project_details.dart';
import 'package:connectone/bai_screens/seller_offers_page.dart';
import 'package:connectone/core/bai_widgets/buyer_quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/history_dialog.dart';
import 'package:connectone/core/bai_widgets/invite_status_dialog.dart';
import 'package:connectone/core/bai_widgets/quote_summary_dialog.dart';
import 'package:connectone/core/bai_widgets/rating_dialog.dart';
import 'package:connectone/core/bai_widgets/report_button.dart';
import 'package:connectone/core/bai_widgets/seller_quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/split_group_name_dialog.dart';
import 'package:connectone/core/bai_widgets/status_dialog.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../bai_widgets/mr_summary_dialog.dart';

class OptionsMenuUtility {
  OptionsMenuUtility({
    required this.context,
    required this.content,
    required this.position,
    required this.prchOrdrId,
  });

  final BuildContext context;
  final Content content;
  final Offset position;
  final List<int> prchOrdrId;

  var isLoading = false;
  List<Datum>? data = [];

  static Future<dynamic>? _currentMenu;

  Future<void> fetchStatusDropdown() async {
    isLoading = true;

    try {
      var api = NetworkController();

      var res = await api.getStatusDropdown(
        content.statusCd ?? '',
        prchOrdrId: content.prchOrdrId?.toString() ?? '',
      );

      data = res.data;
      if ((getRoleLevel() == 1) && isBuyer()) {
        //delete mr
        data?.add(Datum(
          buttonText: "DELETE MR",
          statusName: "DELETE MR",
          statusCode: "DLTD",
          enableAudio: false,
          enablePictures: false,
          enableComments: false,
          enableLocation: false,
        ));
      }

      if (context.mounted) {
        showOptionsMenu(
          context,
          position,
          content: content,
          reload: () {},
        );
      }
    } catch (e) {
      safePrint("Failed to fetch status dropdown: $e");
    } finally {
      isLoading = false;
    }
  }

  bool isEditAllowed() {
    var roleCode = getRoleLevel();
    // var designation = getDesignation().toLowerCase();
    return roleCode == 1 || roleCode == 5 || roleCode == 10;
  }

  void showOptionsMenu(
    BuildContext context,
    Offset offset, {
    required final Content content,
    required Function reload,
  }) {
    // Dismiss any existing menu
    if (_currentMenu != null) {
      Navigator.of(context).pop();
    }

    var menuItems = <PopupMenuEntry<int>>[];
    menuItems = <PopupMenuEntry<int>>[
      if (data != null &&
          data!.isNotEmpty &&
          (data?[0].isOffer == true || data?[0].isNegotiated == true))
        PopupMenuItem<int>(
          value: 101,
          child: Row(
            children: [
              Icon(
                Icons.compare_arrows_rounded,
                color: AppColors.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'NEGOTIATE',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      if (getRoleLevel() == 1 && isBuyer())
        PopupMenuItem<int>(
          value: 100,
          child: Row(
            children: [
              Icon(
                Icons.list,
                color: AppColors.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'SHOW ALL MR',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      PopupMenuItem<int>(
        value: 0,
        child: Row(
          children: [
            Icon(
              Icons.star,
              color: AppColors.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'RATING',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
      if (data != null &&
          data!.isNotEmpty &&
          data?[0].assignVendorButton == true &&
          isBuyer() &&
          ((getRoleLevel() == 1) ||
              (getRoleLevel() == 5) ||
              (getRoleLevel() == 10)))
        PopupMenuItem<int>(
          value: 1,
          child: Row(
            children: [
              Icon(
                Icons.arrow_circle_right_outlined,
                color: AppColors.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'INVITE VENDORS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      PopupMenuItem<int>(
        value: 4,
        child: Row(
          children: [
            Icon(
              Icons.history,
              color: AppColors.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'HISTORY',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
      if (isBuyer())
        PopupMenuItem<int>(
          value: 3,
          child: Row(
            children: [
              Icon(
                Icons.hourglass_bottom,
                color: AppColors.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'QUOTE STATUS',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
    ];
    // Add items from data (status options)
    if (data != null && data!.isNotEmpty) {
      menuItems.addAll(
        data!
            .asMap()
            .entries
            .map((entry) {
              final key = entry.key;
              final value = entry.value;
              // final designation = getDesignation().toString().toLowerCase();
              var roleCode = getRoleLevel();

              if (value.statusCode == "NEWW") {
                if (!isBuyer()) return null;
              } else if ([
                "REJD",
                "EDIT",
                "SPLT",
                "QUOT",
                "NEGO",
                "VASS",
                "PLCD"
              ].contains(value.statusCode)) {
                if (!isBuyer()) return null;
                if (!(roleCode == 1) && !(roleCode == 5) && !(roleCode == 10)) {
                  return null;
                }
              } else if (value.statusCode == "ODCF" ||
                  value.statusCode == "ODDP") {
                if (isBuyer()) return null;
              } else if (["ACPT", "ACWC", "MREJ"].contains(value.statusCode)) {
                if (!isBuyer()) return null;
              } else if (value.statusCode == "CLSD" ||
                  value.statusCode == "PYIN") {
                if (!isBuyer()) return null;
                if (!(roleCode == 1) && !(roleCode == 5) && !(roleCode == 10)) {
                  return null;
                }
              }
              return PopupMenuItem<int>(
                value: key + 5,
                child: Row(
                  children: [
                    Icon(
                      Icons.label,
                      color: AppColors.primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      value.buttonText?.toUpperCase() ?? 'N/A',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              );
            })
            .where((item) => item != null)
            .cast<PopupMenuItem<int>>()
            .toList(),
      );
    }
    menuItems.add(
      PopupMenuItem<int>(
        value: 2,
        child: Row(
          children: [
            Icon(
              Icons.warning_amber,
              color: AppColors.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'REPORT A PROBLEM',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
    _currentMenu = showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy,
        offset.dx + 1,
        offset.dy + 1,
      ),
      items: menuItems,
      elevation: 8.0,
    ).then((value) async {
      _currentMenu = null;
      if (value != null) {
        // if (data != null &&
        //     data!.isNotEmpty &&
        //     data!.any((item) => item.enablePoSummary == true)) {
        //      if(isBuyer()){
        //       Get.to(BuyerOffersPage(item: content, showSummary: true,));
        //      }else{
        //       Get.to(SellerOffersPage(item: content, showSummary: true,));
        //      }
        //      return;
        // }
        switch (value) {
          case 0:
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return RatingDialog(
                  vendorId: content.vendorId!,
                  prchOrdrId: content.prchOrdrId!,
                );
              },
            );
            break;
          case 1:
            var date = await showDialog(
                context: context,
                builder: (context) {
                  return MRSummaryDialog(
                    orderGroupId: content.orderGroupId.toString(),
                  );
                });
            if (date[0] == null) {
              return;
            }

            // Check if the widget is still mounted before showing the dialog
            if (context.mounted) {
              showDialog(
                  context: context,
                  builder: (context) {
                    return QuoteSummaryDialog(
                      prchOrdrId: content.orderGroupId?.toInt() ?? 0,
                      content: content,
                      date: date[0],
                      categoryId: date[1]?.toString(),
                      steelMr: date[3] ?? false,
                    );
                  });
            }
            break;
          case 2:
            showReportDialog(
              context,
              content.prchOrdrId.toString(),
            );
            break;
          case 3:
            showDialog(
                context: context,
                builder: (context) {
                  return InviteStatusDialog(content: content);
                });
            break;
          case 4:
            showDialog(
                context: context,
                builder: (BuildContext context) {
                  return HistoryDialog(
                    prchOrdrId: content.prchOrdrId.toString(),
                    quantity: content.quantity?.toInt(),
                  );
                });
            break;
          case 100:
            Get.to(MrGrouping(
              itemName: content.orderGroupId.toString(),
              itemId: content.orderGroupId.toString(),
            ));
            break;
          case 101:
            if (isBuyer()) {
              showDialog(
                  context: context,
                  builder: (context) {
                    return BuyerQuotesDialog(
                        prchOrdrId: content.prchOrdrId?.toInt() ?? 0);
                  });
            } else {
              showDialog(
                  context: context,
                  builder: (context) {
                    return SellerQuotesDialog(
                      prchOrdrId: content.prchOrdrId?.toInt() ?? 0,
                      itemDetails: Detail(
                        quantity: content.quantity?.toInt(),
                      ),
                    );
                  });
            }
            break;
          default:
            final selectedDatum = data![value - 5];

            if ((selectedDatum.statusName ?? "")
                .toLowerCase()
                .contains("edit")) {
              Get.to(EditProductPage(
                item: content,
                isMr: content.isMr ?? true,
              ));
              return;
            }
            if ((selectedDatum.statusName ?? "")
                .toLowerCase()
                .contains("split")) {
              // Show split group name dialog first
              final splitGroupResult = await showDialog<Map<String, dynamic>>(
                context: context,
                builder: (BuildContext context) {
                  return SplitGroupNameDialog(
                    fixedPrefix: "${content.cappCategoriesName} ",
                    prchOrdrId: content.prchOrdrId?.toInt() ?? 0,
                  );
                },
              );

              // If user confirmed the split, proceed to ProjectDetails
              if (splitGroupResult != null && splitGroupResult['id'] != null) {
                var orderGroupId = content.orderGroupId;
                var category = content.cappCategoriesName;
                var categoryId = content.cappCategoriesId;
                Get.to(ProjectDetails(
                  orderGroupId: orderGroupId?.toInt(),
                  deliveryDate: splitGroupResult['date'],
                  category: category,
                  isMr: content.isMr ?? true,
                  splitSiteId: content.projectId?.toInt(),
                  splitGroupId: splitGroupResult['id'],
                  categoryId: categoryId?.toInt(),
                  splitGroupName: splitGroupResult['name'],
                ));
                savePrchOrdrId(content.prchOrdrId.toString());
                saveOrdrGrpNo(content.orderGrpNo.toString());
              }
              return;
            }

            /// Else show the status change dialog
            // showDialog(
            //   context: context,
            //   builder: (BuildContext context) {
            //     return ChangeStatusDialog(
            //       content: content,
            //       vendorId: content.sellerVendorId ?? 0,
            //       enableAudio: selectedDatum.enableAudio ?? false,
            //       enablePictures: selectedDatum.enablePictures ?? false,
            //       enableComments: selectedDatum.enableComments ?? false,
            //       enableLocation: selectedDatum.enableLocation ?? false,
            //       reload: reload,
            //       statusCd: selectedDatum.statusCode ?? '',
            //       statusName: selectedDatum.buttonText ?? '',
            //     );
            //   },
            // );
            var statusChangeDialog = ChangeStatusDialog(
              prchOrdrId: prchOrdrId,
              content: content,
              vendorId: content.sellerVendorId ?? 0,
              enableAudio: selectedDatum.enableAudio ?? false,
              enablePictures: selectedDatum.enablePictures ?? false,
              enableComments: selectedDatum.enableComments ?? false,
              enableLocation: selectedDatum.enableLocation ?? false,
              reload: reload,
              statusCd: selectedDatum.statusCode ?? '',
              statusName: selectedDatum.buttonText ?? '',
              assignedVendorId: null,
            );

            if ((selectedDatum.statusName ?? "")
                .toLowerCase()
                .contains("reject")) {
              showDialog(
                context: context,
                builder: (context) => statusChangeDialog,
              );
              return;
            }

            if (selectedDatum.statusCode == "DLTD") {
              showDialog(
                context: context,
                builder: (context) => statusChangeDialog,
              );
              return;
            }

            bool enablePoSummary = selectedDatum.enablePoSummary ?? false;
            var buttonText = selectedDatum.buttonText ?? '';
            Widget offersPage;

            if (isBuyer()) {
              offersPage = BuyerOffersPage(
                item: content,
                showSummary: false,
                changeStatusDialog: statusChangeDialog,
                enablePoSummary: enablePoSummary,
                buttonText: buttonText,
                categoryId: null,
                splitName: null,
                steelMr: false,
              );
            } else {
              offersPage = SellerOffersPage(
                item: content,
                showSummary: false,
                changeStatusDialog: statusChangeDialog,
                enablePoSummary: enablePoSummary,
                buttonText: buttonText,
                categoryId: null,
                splitName: null,
                steelMr: false,
              );
            }

            Get.to(offersPage);
        }
      }
    });
  }
}
