import 'package:connectone/bai_screens/profile_screen.dart';
import 'package:connectone/core/old_widgets/common/show_feedback_dialog.dart';
import 'package:connectone/core/utils/data_storage.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/app_routes.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/navigate_safe.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../utils/colors.dart';
import 'navigation_drawer_tile.dart';

class SideDrawer extends StatelessWidget {
  final String userName;
  final NetworkController networkController = NetworkController();

  SideDrawer({Key? key, required this.userName}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: getMediumTheme(),
      child: Drawer(
        backgroundColor: AppColors.primaryColor,
        child: ListView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.zero,
          children: [
            _buildDrawerHeader(context),
            _buildDrawerItems(context),
            const SizedBox(height: 40),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    var style = const TextStyle(
      color: Colors.white,
      fontSize: 14,
    );

    return InkWell(
      onTap: () {
        Get.to(const ProfileScreen());
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        color: AppColors.primaryColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 48),
            _buildProfileImage(),
            const SizedBox(height: 12.0),
            _buildOrganizationName(),
            const SizedBox(height: 12),
            _buildUserNameOrLogin(context),
            const SizedBox(height: 4),
            Text(
              getVendorName(),
              style: style,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              "Role: ${getDesignation().trim()}",
              style: style,
            ),
            const SizedBox(height: 4),
            Text(
              "Type: ${getCustomerRole().toString().convertAbbreviation()}",
              style: style,
            ),
            const SizedBox(height: 12),
            const Divider(
              height: 0.5,
              thickness: 0.5,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Container(
      height: 64,
      width: 64,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        image: DecorationImage(
          image: networkController.organisationData?.orderingAppUrl != null
              ? NetworkImage(
                  networkController.organisationData?.orderingAppUrl ?? "")
              : const AssetImage('assets/images/no_image_available.jpeg')
                  as ImageProvider,
          fit: BoxFit.cover,
        ),
        boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 5.0)],
      ),
    );
  }

  Widget _buildOrganizationName() {
    return Text(
      networkController.organisationData?.organizationName ?? "",
      style: const TextStyle(
        color: Colors.white,
        fontSize: 12,
      ),
    );
  }

  Widget _buildUserNameOrLogin(BuildContext context) {
    return isLoggedIn()
        ? Text(
            "" + getUserName() ?? "User",
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
            maxLines: 1,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          )
        : Expanded(
            child: TextButton(
              child: Text("Login",
                  style: TextStyle(color: AppColors.primaryColor)),
              onPressed: () {
                Navigator.pop(context);
                Get.toNamed(AppRoutes.loginScreen);
              },
            ),
          );
  }

  Widget _buildDrawerItems(BuildContext context) {
    return Column(
      children: [
        // NavigationDrawerTileItem(
        //   icon: Icons.account_circle_outlined,
        //   title: "My Account",
        //   onTap: () => Get.to(const ProfileScreen()),
        // ),
        NavigationDrawerTileItem(
          icon: Icons.account_circle_outlined,
          title: "My Account",
          onTap: () => Get.toNamed(AppRoutes.profile),
        ),
        // NavigationDrawerTileItem(
        //   icon: Icons.checklist_outlined,
        //   title: "My Stocks",
        //   onTap: () => _onTileTap(context, AppRoutes.myStockIndexButton),
        // ),
        // NavigationDrawerTileItem(
        //   icon: Icons.sell_outlined,
        //   title: "Sold Outs",
        //   onTap: () => _onTileTap(context, AppRoutes.outStock),
        // ),
        // if (marginEnabled())
        //   NavigationDrawerTileItem(
        //     icon: Icons.straighten_outlined,
        //     title: "Margin",
        //     onTap: () => _onTileTap(context, AppRoutes.marginaldetails),
        //   ),
        // NavigationDrawerTileItem(
        //   icon: Icons.insights_outlined,
        //   title: "Live Auction Stats",
        //   onTap: () => _onTileTap(context, AppRoutes.liveAuctionStat),
        // ),
        NavigationDrawerTileItem(
          icon: Icons.notifications_outlined,
          title: "Notifications",
          onTap: () => _onTileTap(context, AppRoutes.notificationScreen),
        ),
        NavigationDrawerTileItem(
          icon: Icons.feedback_outlined,
          title: "Feedback",
          onTap: () => _onFeedbackTap(context),
        ),
        // NavigationDrawerTileItem(
        //   icon: Icons.contact_phone_outlined,
        //   title: "Contact Us",
        //   onTap: () => _onContactUsTap(context),
        // ),
        NavigationDrawerTileItem(
          icon: Icons.settings_outlined,
          title: "Settings",
          onTap: () => _onTileTap(context, AppRoutes.settingScreen),
        ),
        _buildLoginLogoutItem(context),
      ],
    );
  }

  Widget _buildLoginLogoutItem(BuildContext context) {
    return Visibility(
      visible: isLoggedIn(),
      replacement: NavigationDrawerTileItem(
        icon: Icons.logout_outlined,
        title: "Login",
        onTap: () {
          Navigator.pop(context);
          Get.toNamed(AppRoutes.loginScreen);
        },
      ),
      child: NavigationDrawerTileItem(
        icon: Icons.logout_outlined,
        title: "Logout",
        onTap: () => _onLogoutTap(context),
      ),
    );
  }

  Widget _buildFooter() {
    return const Column(
      children: [
        Text(
          "Powered by",
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
        SizedBox(height: 16.0),
        Text(
          "BAI Store",
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.0),
        Text(
          "26-05-25 V1 PROD",
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
        SizedBox(height: 16.0),
      ],
    );
  }

  void _onTileTap(BuildContext context, String route) {
    Navigator.pop(context);
    navigateSafe(route);
  }

  void _onFeedbackTap(BuildContext context) {
    Navigator.pop(context);
    if (isLoggedIn()) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return const ShowFeedbackDialogue(
            title: "Thank you!",
            stockId: '-1',
          );
        },
      );
    } else {
      Get.offAllNamed(AppRoutes.loginScreen);
    }
  }

  void _onContactUsTap(BuildContext context) async {
    Navigator.pop(context);
    var url = DataStorage.configData
            ?.firstWhere(
                (element) => element?.keyName1 == "contact_us_page_link")
            ?.valueName1 ??
        additionalUrl;
    Get.toNamed(AppRoutes.helpScreen1, arguments: ["CONTACTUS", url]);
  }

  void _onLogoutTap(BuildContext context) {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text(
          'Logout',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        content: const Text('Are you sure you want to logout?'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, 'Cancel'),
            child: const Text(
              'Cancel',
              style: TextStyle(
                color: AppColors.primaryColorOld,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              await networkController.deleteToken();
              clearAuthToken();
              writeToStorage(loggedIn, "FALSE");
              Navigator.pop(context);
              Get.offAllNamed(AppRoutes.loginScreen);
            },
            child: Text(
              'Logout',
              style: TextStyle(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
