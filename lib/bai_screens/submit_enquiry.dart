import 'dart:io';
import 'dart:ui';

import 'package:connectone/bai_blocs/insert_stock/cubit/insert_stock_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/insert_stock_res.dart';
import 'package:connectone/bai_screens/continue_shopping.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';
import '../core/utils/app_routes.dart';

class SubmitEnquiryScreen extends StatefulWidget {
  const SubmitEnquiryScreen({
    Key? key,
    required this.isMr,
    this.deliveryDate,
  }) : super(key: key);

  final bool isMr;
  final DateTime? deliveryDate;

  @override
  State<SubmitEnquiryScreen> createState() => _SubmitEnquiryScreenState();
}

class _SubmitEnquiryScreenState extends State<SubmitEnquiryScreen> {
  final _formKey = GlobalKey<FormState>();

  // TextEditingController can be used to control the input fields
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController siteNameController = TextEditingController();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController address1Controller = TextEditingController();
  final TextEditingController address2Controller = TextEditingController();
  final TextEditingController apartmentController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController pinController = TextEditingController();

  String? selectedCountry;
  String? selectedState = "Kerala";

  List<String> countries = ['India'];
  List<String> states = ['Kerala'];

  @override
  void initState() {
    super.initState();
    context.read<InsertStockCubit>().reset();
    address1Controller.text = BaiCart.project?.address?.city ?? "";
    phoneController.text = getVendorPhone();
    firstNameController.text = getVendorName();
    siteNameController.text = BaiCart.project?.projectName ?? "";
    cityController.text = BaiCart.project?.address?.city ?? "";
    if (BaiCart.project?.address?.pincode != null) {
      pinController.text = BaiCart.project?.address?.pincode ?? "";
    }
    if (BaiCart.project?.address?.sellingAddressLine1 != null) {
      address1Controller.text =
          BaiCart.project?.address?.sellingAddressLine1 ?? "";
    }
    if (BaiCart.project?.address?.sellingAddressLine2 != null) {
      address2Controller.text =
          BaiCart.project?.address?.sellingAddressLine2 ?? "";
    }
    pinController.text = BaiCart.project?.address?.pincode ?? "";
    if (countries.contains(BaiCart.project?.address?.country)) {
      selectedCountry = BaiCart.project?.address?.country;
    }
    if (states.contains(BaiCart.project?.address?.state)) {
      selectedState = BaiCart.project?.address?.state;
    }
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Creating a new request is easy!\n\nYour cart items are listed at the top - verify quantities and specifications.\n\nIn the Contact section:\n• Enter your mobile number for delivery updates\n\nIn the Delivery section:\n• Provide site name and complete address\n• Select state and PIN code\n\nAll fields marked with * are mandatory for successful submission.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  void _showPurchaseEnquiryPostedDialog(
    BuildContext context,
    String enquiryId,
    InsertStockRes res,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "${widget.isMr ? "Material" : "Service"} Request Posted",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
              "Your ${widget.isMr ? "Material" : "Service"} enquiry (ID: $enquiryId) has been sent to your team admin for approval."),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                var add = Address(
                  addressLine1: address1Controller.text.trim(),
                  addressLine2: address2Controller.text.trim(),
                  city: BaiCart.project?.address?.city?.trim(),
                  // country: selectedCountry!.trim(),
                  country: "India",
                  state: selectedState!.trim(),
                  pincode: pinController.text.trim(),
                  latitude: BaiCart.project?.address?.latitude,
                  longitude: BaiCart.project?.address?.longitude,
                  cityLan: BaiCart.project?.address?.latitude,
                  cityLong: BaiCart.project?.address?.longitude,
                );
                var req = InsertStockReq(
                  projectId: BaiCart.project?.id ?? 0,
                  projectName: BaiCart.project?.projectName?.trim(),
                  projectAddress: add,
                  siteAccess: BaiCart.siteAccess?.trim(),
                  roadAccess: BaiCart.siteAccess?.trim(),
                  deliveryDate: BaiCart.deliveryDate ?? DateTime.now(),
                  // deliveryDate: formatDateTime(BaiCart.deliveryDate ?? DateTime.now()),
                  customerId: int.parse(getCustomerId()),
                  shippingAddress: add,
                  poStockItems: BaiCart.cartItems
                      .map((element) => element.poStockItem!)
                      .toList(),
                );
                Get.to(ContinueShoppingScreen(
                  insertStockRes: res,
                  mobileNumber: phoneController.text,
                  address: add,
                  req: req,
                  isMr: widget.isMr,
                  deliveryDate: widget.deliveryDate,
                ));
                if(!BaiCart.hasSplitItems()) BaiCart.cartItems.clear();
              },
              child: Text(
                "OK",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override

  /// Builds the UI for the submit enquiry page.
  ///
  /// The page displays the products in the cart and allows the user to enter
  /// their contact information, delivery address and submit the enquiry.
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create ${widget.isMr ? "Material" : "Service"} Request'),
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Get.back();
          },
        ),
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<InsertStockCubit, InsertStockState>(
          listener: (context, state) {
            if (state is InsertStockSuccess) {
              _showPurchaseEnquiryPostedDialog(
                context,
                // state.response.orders
                //         ?.map((order) => order.id.toString())
                //         .join(', ') ??
                //     '',
                state.response.orders?[0].orderGrpName ?? '',
                state.response,
              );
            }
          },
          builder: (context, state) {
            (state is InsertStockLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            return SizedBox(
              height: MediaQuery.of(context).size.height - 72,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        /// Display the list of products in the cart
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            var item = BaiCart.cartItems[index];
                            return SubmitEnquiryProductItem(
                              quantity: item.poStockItem?.quantity ?? 0,
                              productName: item.poStockItem?.mvtItemName ?? "-",
                              unit: item.selectedUnit ?? "-",
                              instructions:
                                  item.poStockItem?.instructions ?? "-",
                              image: item.images!.isNotEmpty
                                  ? item.images!.first
                                  : null,
                              poStockItem: item.poStockItem,
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 20);
                          },
                          itemCount: BaiCart.cartItems.length,
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          "Contact",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),

                        /// Display the contact information fields
                        SubmitEnquiryTextField(
                          controller: phoneController,
                          labelText: 'Mobile Phone Number',
                          maxLength: 10,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your phone number';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          "Delivery",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        /// Display the delivery address fields
                        // SubmitEnquiryTextField(
                        //   controller: firstNameController,
                        //   labelText: 'Business Name',
                        //   validator: (value) {
                        //     if (value == null || value.isEmpty) {
                        //       return 'Please enter your name';
                        //     }
                        //     return null;
                        //   },
                        // ),
                        // const SizedBox(height: 16),
                        SubmitEnquiryTextField(
                          controller: siteNameController,
                          labelText: 'Site Name',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter site name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        SubmitEnquiryTextField(
                          controller: address1Controller,
                          labelText: 'Site Address Line 1',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your address';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        SubmitEnquiryTextField(
                          controller: address2Controller,
                          labelText: 'Site Address Line 2',
                          // validator: (value) {
                          //   if (value == null || value.isEmpty) {
                          //     return 'Please enter address line 2';
                          //   }
                          //   return null;
                          // },
                        ),
                        const SizedBox(height: 16),
                        SubmitEnquiryTextField(
                          controller: cityController,
                          labelText: 'City',
                          // validator: (value) {
                          //   if (value == null || value.isEmpty) {
                          //     return 'Please enter your city';
                          //   }
                          //   return null;
                          // },
                        ),
                        const SizedBox(height: 16),
                        CustomDropdown(
                          value: selectedState,
                          items: states,
                          labelText: 'State',
                          hintText: 'Select State',
                          onChanged: (value) {
                            setState(() {
                              selectedState = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Please select a state';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        SubmitEnquiryTextField(
                          controller: pinController,
                          labelText: 'PIN',
                          maxLength: 6,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your PIN';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        // BaiCart.hasSplitItems()
                        //     ? BaiButton(
                        //         onTap: () {
                        //           Get.until((route) =>
                        //               route.settings.name ==
                        //               AppRoutes.offlineScreen);
                        //         },
                        //         text: "NEXT SPLIT",
                        //         backgoundColor: Colors.black54,
                        //       )
                        //     : const SizedBox.shrink(),
                        // BaiCart.hasSplitItems()
                        //     ? const SizedBox(height: 20)
                        //     : const SizedBox.shrink(),
                        BaiButton(
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              var add = Address(
                                addressLine1: address1Controller.text.trim(),
                                addressLine2: address2Controller.text.trim(),
                                city: BaiCart.project?.address?.city?.trim(),
                                country: "India",
                                state: selectedState!.trim(),
                                pincode: pinController.text.trim(),
                                latitude: BaiCart.project?.address?.latitude,
                                longitude: BaiCart.project?.address?.longitude,
                                cityLan: BaiCart.project?.address?.latitude,
                                cityLong: BaiCart.project?.address?.longitude,
                              );

                              var req = InsertStockReq(
                                projectId: BaiCart.project?.id ?? 0,
                                orderGroupId: BaiCart.orderGroupId,
                                projectName:
                                    BaiCart.project?.projectName?.trim(),
                                projectAddress: add,
                                siteAccess: BaiCart.siteAccess?.trim(),
                                roadAccess: BaiCart.siteAccess?.trim(),
                                deliveryDate:
                                    BaiCart.deliveryDate ?? DateTime.now(),
                                // deliveryDate: formatDateTime(BaiCart.deliveryDate ?? DateTime.now()),
                                customerId: int.parse(getCustomerId()),
                                shippingAddress: add,
                                poStockItems: BaiCart.cartItems
                                    .map((element) => element.poStockItem!)
                                    .toList(),
                              );
                              if (widget.deliveryDate != null) {
                                req.prchOrdrId =
                                    int.tryParse(getPrchOrdrId().toString()) ??
                                        0;
                                req.orderGrpNo =
                                    int.tryParse(getOrdrGrpNo().toString());
                              }
                              context
                                  .read<InsertStockCubit>()
                                  .insertStocks(req);
                              // Get.to(const ContinueShoppingScreen());
                            } else {
                              setState(() {});
                            }
                          },
                          text: BaiCart.hasSplitItems()
                              ? "COMPLETE SPLIT"
                              : 'Create ${widget.isMr ? "Material" : "Service"} Request',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year.toString().padLeft(4, '0')}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
}

class SubmitEnquiryTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final int? maxLength;

  const SubmitEnquiryTextField({
    Key? key,
    required this.controller,
    required this.labelText,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.maxLength,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      maxLength: maxLength,
      decoration: InputDecoration(
          labelText: labelText,
          border: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          isDense: true,
          labelStyle: const TextStyle(color: Colors.black)),
      style: const TextStyle(color: Colors.black),
    );
  }
}

class CustomDropdown extends StatelessWidget {
  final String? value;
  final List<String> items;
  final String labelText;
  final String hintText;
  final ValueChanged<String?> onChanged;
  final String? Function(String?)? validator;

  const CustomDropdown({
    Key? key,
    required this.value,
    required this.items,
    required this.labelText,
    required this.hintText,
    required this.onChanged,
    this.validator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
          labelText: labelText,
          border: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.black,
              width: 1,
            ),
          ),
          isDense: true,
          labelStyle: const TextStyle(color: Colors.black)),
      hint: Text(hintText),
      items: items.map((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }
}

class SubmitEnquiryProductItem extends StatelessWidget {
  final int quantity;
  final String productName;
  final String unit;
  final String? instructions;
  final String? image;
  final PoStockItem? poStockItem;

  const SubmitEnquiryProductItem({
    Key? key,
    required this.quantity,
    required this.productName,
    required this.unit,
    this.instructions,
    this.image,
    this.poStockItem,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: image == null
                      ? Icon(
                          Icons.image,
                          color: Colors.grey.shade500,
                          size: 30,
                        )
                      : kIsWeb
                          ? Image.network(
                              image!,
                              fit: BoxFit.cover,
                            )
                          : Image.file(
                              File(image!),
                              fit: BoxFit.cover,
                            ),
                ),
                Positioned(
                  top: -10,
                  right: -10,
                  child: CircleAvatar(
                    radius: 10,
                    backgroundColor: Colors.grey.shade600,
                    child: Text(
                      '$quantity',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (instructions != null && instructions!.isNotEmpty)
                    Text(
                      'Instructions: $instructions',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: (poStockItem?.variants ?? [])
                        .fold<Map<String, List<String>>>({}, (map, item) {
                          map
                              .putIfAbsent(item.optionGroupName ?? "", () => [])
                              .add(item.optionName ?? "");
                          return map;
                        })
                        .entries
                        .map<Widget>((entry) {
                          return Text(
                            '${entry.key}: ${entry.value.join(', ')}',
                            style: const TextStyle(
                              fontWeight: FontWeight.normal,
                              fontSize: 12,
                            ),
                          );
                        })
                        .toList(),
                  )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
