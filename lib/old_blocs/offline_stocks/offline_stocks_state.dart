part of 'offline_stocks_bloc.dart';

abstract class OfflineMainState extends Equatable {
  const OfflineMainState();
}

class OfflineStocksInitial extends OfflineMainState {
  @override
  List<Object> get props => [];
}

class OfflineStocksLoaded extends OfflineMainState {
  final BaiProductsRes offlineStocks;
  final List<GroupedProduct>? groups;
  final List<FavouriteItem>? favouritesResponse;
  final String lotNo;
  final String deliveryDate;
  final String stockId;
  final int selectedItem;

  const OfflineStocksLoaded({
    required this.offlineStocks,
    required this.favouritesResponse,
    this.stockId = "",
    this.deliveryDate = "",
    this.lotNo = "",
    this.selectedItem = 0,
    this.groups = const [],
  });

  @override
  List<Object> get props =>
      [offlineStocks, stockId, deliveryDate, lotNo, selectedItem];
}

class OfflineStocksRefresh extends OfflineMainState {
  @override
  List<Object> get props => [];
}
