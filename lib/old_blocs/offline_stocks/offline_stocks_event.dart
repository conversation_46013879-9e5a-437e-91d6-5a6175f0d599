part of 'offline_stocks_bloc.dart';

abstract class OfflineMainEvent extends Equatable {
  const OfflineMainEvent();
}

class InitializeOfflineStocks extends OfflineMainEvent {
  final BaiProductsRes? offlineStocks;
  final String lotNo;
  final String deliveryDate;
  final String product;
  final String grade;
  final String location;
  final String stockId;
  final String quantity;
  final String priceRange;
  final String buyBidPrice;
  final String category;
  final String sortBy;
  final String queryString;
  final String code;

  const InitializeOfflineStocks({
    this.offlineStocks,
    this.stockId = "",
    this.deliveryDate = "",
    this.lotNo = "",
    this.product = "",
    this.location = "",
    this.grade = "",
    this.quantity = "",
    this.priceRange = "",
    this.buyBidPrice = "",
    this.category = "",
    this.sortBy = "NONE",
    this.queryString = "",
    required this.code,
  });

  @override
  List<Object?> get props =>
      [offlineStocks, stockId, lotNo, deliveryDate, sortBy];
}

class InitializeOfflineStocksNext extends OfflineMainEvent {
  final BaiProductsRes? offlineStocks;
  final String lotNo;
  final String deliveryDate;
  final String product;
  final String grade;
  final String location;
  final String stockId;
  final String quantity;
  final String priceRange;
  final String buyBidPrice;
  final String category;
  final String sortBy;
  final String queryString;
  final String code;

  const InitializeOfflineStocksNext({
    this.offlineStocks,
    this.stockId = "",
    this.deliveryDate = "",
    this.lotNo = "",
    this.product = "",
    this.location = "",
    this.grade = "",
    this.quantity = "",
    this.priceRange = "",
    this.buyBidPrice = "",
    this.category = "",
    this.sortBy = "NONE",
    this.queryString = "",
    required this.code,
  });

  @override
  List<Object?> get props =>
      [offlineStocks, stockId, lotNo, deliveryDate, sortBy];
}

class SortOfflineStocks extends OfflineMainEvent {
  final BaiProductsRes offlineStocks;
  final String sortBy;

  const SortOfflineStocks(this.offlineStocks, this.sortBy);

  @override
  List<Object?> get props => [offlineStocks, sortBy];
}

class SearchOfflineStocks extends OfflineMainEvent {
  final BaiProductsRes offlineStocks;
  final String lotNo;
  final String deliveryDate;
  final String stockId;

  const SearchOfflineStocks(
      {required this.offlineStocks,
      this.stockId = "",
      this.deliveryDate = "",
      this.lotNo = ""});

  @override
  List<Object?> get props => [offlineStocks, stockId, lotNo, deliveryDate];
}

class FavouritesClicked extends OfflineMainEvent {
  const FavouritesClicked();

  @override
  List<Object?> get props => [];
}

class ItemSelected extends OfflineMainEvent {
  final int selectedItem;

  const ItemSelected(this.selectedItem);

  @override
  List<Object?> get props => [selectedItem];
}

class SetDeliveryDate extends OfflineMainEvent {
  final String deliveryDate;

  const SetDeliveryDate(this.deliveryDate);

  @override
  List<Object?> get props => [deliveryDate];
}

class QrScan extends OfflineMainEvent {
  const QrScan();

  @override
  List<Object?> get props => [];
}

class MarkAsFavourite extends OfflineMainEvent {
  final String customerId;
  final String stockId;
  final String code;

  const MarkAsFavourite(this.customerId, this.stockId, this.code);

  @override
  List<Object?> get props => [];
}

class UnMarkAsFavourite extends OfflineMainEvent {
  final String customerId;
  final String stockId;
  final String code;

  const UnMarkAsFavourite(this.customerId, this.stockId, this.code);

  @override
  List<Object?> get props => [];
}

class RefreshOfflineStocks extends OfflineMainEvent {
  const RefreshOfflineStocks();

  @override
  List<Object?> get props => [];
}
